#!/usr/bin/env python3
"""
Demo 06: <PERSON>rro<PERSON> and Edge Cases Test
===========================================

This script tests browser-use error handling and edge cases:
- Invalid URLs
- Network timeouts
- Missing elements
- JavaScript-heavy sites
- Rate limiting scenarios

Expected: Shows how browser-use handles various error conditions gracefully.
"""

import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatOpenAI

class ErrorTester:
    """Helper class for testing error scenarios"""
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY required for error testing")
    
    async def run_error_test(self, test_name, task, expected_behavior):
        """Run a single error test"""
        print(f"\n🧪 Testing: {test_name}")
        print(f"📋 Task: {task}")
        print(f"🎯 Expected: {expected_behavior}")
        
        browser_session = None
        try:
            # Create browser session
            browser_profile = BrowserProfile(
                headless=False,
                user_data_dir=f'~/.config/browseruse/profiles/test_error_{test_name.lower().replace(" ", "_")}',
            )
            browser_session = BrowserSession(browser_profile=browser_profile)
            
            # Create agent
            llm = ChatOpenAI(model='gpt-4o-mini', api_key=self.api_key)
            agent = Agent(task=task, llm=llm, browser_session=browser_session)
            
            # Run with timeout
            history = await asyncio.wait_for(agent.run(), timeout=60)
            
            # Analyze result
            if history.final_result:
                print(f"✅ Result: {history.final_result[:200]}...")
                success = True
            else:
                print("⚠️  No final result returned")
                success = False
            
            # Take screenshot
            screenshot_path = Path('demos/screenshots')
            screenshot_path.mkdir(exist_ok=True)
            
            page = await browser_session.get_page()
            screenshot_file = f'06_error_{test_name.lower().replace(" ", "_")}.png'
            await page.screenshot(path=str(screenshot_path / screenshot_file))
            
            return {
                'test': test_name,
                'success': success,
                'result': history.final_result,
                'steps': len(history.history),
                'error': None
            }
            
        except asyncio.TimeoutError:
            print("⏰ Test timed out (60s)")
            return {
                'test': test_name,
                'success': False,
                'result': None,
                'steps': 0,
                'error': 'Timeout'
            }
            
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            return {
                'test': test_name,
                'success': False,
                'result': None,
                'steps': 0,
                'error': str(e)
            }
            
        finally:
            if browser_session:
                await browser_session.close()

async def test_error_scenarios():
    """Test various error scenarios"""
    print("🚀 Starting error handling tests...")
    
    tester = ErrorTester()
    results = []
    
    # Test 1: Invalid URL
    result = await tester.run_error_test(
        "Invalid URL",
        "Go to https://this-domain-definitely-does-not-exist-12345.com and tell me what you see",
        "Should handle DNS resolution failure gracefully"
    )
    results.append(result)
    
    # Test 2: Slow loading site
    result = await tester.run_error_test(
        "Slow Site",
        "Go to httpbin.org/delay/10 and tell me what response you get",
        "Should wait for slow responses or timeout gracefully"
    )
    results.append(result)
    
    # Test 3: Missing element
    result = await tester.run_error_test(
        "Missing Element",
        "Go to example.com and click on a button with text 'This Button Does Not Exist'",
        "Should report that the element cannot be found"
    )
    results.append(result)
    
    # Test 4: JavaScript-heavy site
    result = await tester.run_error_test(
        "JavaScript Heavy",
        "Go to react.dev and find the main heading on the homepage",
        "Should handle JavaScript-rendered content"
    )
    results.append(result)
    
    # Test 5: Redirect handling
    result = await tester.run_error_test(
        "Redirect Chain",
        "Go to httpbin.org/redirect/3 and tell me what final URL you end up at",
        "Should follow redirects and report final destination"
    )
    results.append(result)
    
    return results

def analyze_error_results(results):
    """Analyze and report on error test results"""
    print("\n" + "="*70)
    print("ERROR HANDLING TEST ANALYSIS")
    print("="*70)
    
    for result in results:
        print(f"\n🧪 {result['test']}")
        print(f"   Status: {'✅ Handled' if result['success'] else '❌ Failed'}")
        print(f"   Steps: {result['steps']}")
        
        if result['error']:
            print(f"   Error: {result['error']}")
        
        if result['result']:
            preview = result['result'][:100] + "..." if len(result['result']) > 100 else result['result']
            print(f"   Result: {preview}")
    
    # Summary
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"\n📊 Summary: {successful}/{total} error scenarios handled successfully")
    
    if successful == total:
        print("🎉 Excellent error handling!")
    elif successful > total // 2:
        print("👍 Good error handling with room for improvement")
    else:
        print("⚠️  Error handling needs improvement")

async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 06: Error Handling Test")
    print("=" * 50)
    
    try:
        results = await test_error_scenarios()
        analyze_error_results(results)
        
    except ValueError as e:
        print(f"❌ Setup error: {e}")
        return
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nPress Enter to exit...")
    input()

if __name__ == '__main__':
    asyncio.run(main())
