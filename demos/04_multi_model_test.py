#!/usr/bin/env python3
"""
Demo 04: Multi-Model Comparison Test
====================================

This script tests different AI models with the same task:
- OpenAI GPT-4
- Anthropic Claude
- Google Gemini
- Comparing performance, speed, and results

Expected: Shows how different models handle the same browser task.
"""

import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatOpenAI, ChatAnthropic, ChatGoogle

class ModelTester:
    """Helper class to test different models"""
    
    def __init__(self):
        self.results = {}
        
    async def test_model(self, model_name, llm, task):
        """Test a specific model with given task"""
        print(f"\n🧠 Testing {model_name}...")
        
        browser_session = None
        start_time = time.time()
        
        try:
            # Create fresh browser session for each model
            browser_profile = BrowserProfile(
                headless=False,
                user_data_dir=f'~/.config/browseruse/profiles/test_{model_name.lower().replace(" ", "_")}',
            )
            browser_session = BrowserSession(browser_profile=browser_profile)
            
            # Create and run agent
            agent = Agent(
                task=task,
                llm=llm,
                browser_session=browser_session,
            )
            
            history = await agent.run()
            end_time = time.time()
            
            # Collect results
            result = {
                'model': model_name,
                'success': True,
                'duration': end_time - start_time,
                'final_result': history.final_result,
                'steps': len(history.history),
                'usage': getattr(history, 'usage', None),
                'error': None
            }
            
            # Take screenshot
            screenshot_path = Path('demos/screenshots')
            screenshot_path.mkdir(exist_ok=True)
            
            page = await browser_session.get_page()
            screenshot_file = f'04_model_{model_name.lower().replace(" ", "_")}.png'
            await page.screenshot(path=str(screenshot_path / screenshot_file))
            
            print(f"✅ {model_name} completed in {result['duration']:.1f}s")
            print(f"   Steps: {result['steps']}")
            print(f"   Result: {result['final_result'][:100]}..." if result['final_result'] else "   No result")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            print(f"❌ {model_name} failed: {e}")
            
            return {
                'model': model_name,
                'success': False,
                'duration': end_time - start_time,
                'final_result': None,
                'steps': 0,
                'usage': None,
                'error': str(e)
            }
            
        finally:
            if browser_session:
                await browser_session.close()

async def test_all_models():
    """Test all available models"""
    print("🚀 Starting multi-model comparison test...")
    
    # Define the task - same for all models
    task = "Go to news.ycombinator.com and find the title of the top story"
    print(f"📋 Task for all models: {task}")
    
    tester = ModelTester()
    results = []
    
    # Test OpenAI GPT-4
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        try:
            llm = ChatOpenAI(model='gpt-4o-mini', api_key=openai_key)
            result = await tester.test_model('OpenAI GPT-4o-mini', llm, task)
            results.append(result)
        except Exception as e:
            print(f"❌ OpenAI setup failed: {e}")
    else:
        print("⚠️  Skipping OpenAI - no API key")
    
    # Test Anthropic Claude
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    if anthropic_key:
        try:
            llm = ChatAnthropic(model='claude-3-haiku-20240307', api_key=anthropic_key)
            result = await tester.test_model('Anthropic Claude Haiku', llm, task)
            results.append(result)
        except Exception as e:
            print(f"❌ Anthropic setup failed: {e}")
    else:
        print("⚠️  Skipping Anthropic - no API key")
    
    # Test Google Gemini
    google_key = os.getenv('GOOGLE_API_KEY')
    if google_key:
        try:
            llm = ChatGoogle(model='gemini-1.5-flash', api_key=google_key)
            result = await tester.test_model('Google Gemini Flash', llm, task)
            results.append(result)
        except Exception as e:
            print(f"❌ Google setup failed: {e}")
    else:
        print("⚠️  Skipping Google - no API key")
    
    return results

def print_comparison(results):
    """Print comparison of results"""
    print("\n" + "="*70)
    print("MODEL COMPARISON RESULTS")
    print("="*70)
    
    if not results:
        print("❌ No results to compare")
        return
    
    # Sort by success, then by duration
    results.sort(key=lambda x: (not x['success'], x['duration']))
    
    print(f"{'Model':<20} {'Success':<8} {'Time':<8} {'Steps':<6} {'Result Preview'}")
    print("-" * 70)
    
    for result in results:
        success = "✅ Yes" if result['success'] else "❌ No"
        duration = f"{result['duration']:.1f}s"
        steps = str(result['steps'])
        preview = (result['final_result'][:30] + "...") if result['final_result'] else "None"
        
        print(f"{result['model']:<20} {success:<8} {duration:<8} {steps:<6} {preview}")
    
    # Print winner
    successful_results = [r for r in results if r['success']]
    if successful_results:
        fastest = min(successful_results, key=lambda x: x['duration'])
        print(f"\n🏆 Fastest successful model: {fastest['model']} ({fastest['duration']:.1f}s)")

async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 04: Multi-Model Test")
    print("=" * 50)
    
    results = await test_all_models()
    print_comparison(results)
    
    print("\nPress Enter to exit...")
    input()

if __name__ == '__main__':
    asyncio.run(main())
