#!/usr/bin/env python3
"""
Demo 07: Working Features Demonstration
=======================================

This script demonstrates all the browser-use features that ARE working,
focusing on browser automation without AI agents (since AI has API issues).

Expected: Shows comprehensive browser automation capabilities.
"""

import asyncio
import time
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use.browser import BrowserProfile, BrowserSession

class BrowserDemo:
    """Demonstrates working browser automation features"""
    
    def __init__(self):
        self.results = []
        
    async def demo_basic_navigation(self):
        """Demo basic navigation and page interaction"""
        print("🌐 Testing basic navigation...")
        
        browser_session = None
        try:
            browser_profile = BrowserProfile(
                headless=False,
                user_data_dir='~/.config/browseruse/profiles/demo_nav',
            )
            browser_session = BrowserSession(browser_profile=browser_profile)
            
            # Get current page
            page = await browser_session.get_current_page()
            
            # Navigate to different sites
            sites = [
                "https://example.com",
                "https://httpbin.org",
                "https://jsonplaceholder.typicode.com"
            ]
            
            for i, site in enumerate(sites):
                print(f"  📍 Navigating to {site}...")
                await page.goto(site)
                await asyncio.sleep(2)  # Wait for page load
                
                # Get page info
                title = await page.title()
                url = page.url
                print(f"    ✅ Title: {title}")
                print(f"    🔗 URL: {url}")
                
                # Take screenshot
                screenshot_path = Path('demos/screenshots')
                screenshot_path.mkdir(exist_ok=True)
                await page.screenshot(path=str(screenshot_path / f'07_nav_{i+1}.png'))
            
            return True
            
        except Exception as e:
            print(f"    ❌ Navigation demo failed: {e}")
            return False
            
        finally:
            if browser_session:
                await browser_session.close()
    
    async def demo_multi_tab_management(self):
        """Demo multi-tab functionality"""
        print("🗂️  Testing multi-tab management...")
        
        browser_session = None
        try:
            browser_profile = BrowserProfile(
                headless=False,
                user_data_dir='~/.config/browseruse/profiles/demo_tabs',
            )
            browser_session = BrowserSession(browser_profile=browser_profile)
            
            # Create multiple tabs
            sites = [
                "https://example.com",
                "https://httpbin.org/html",
                "https://jsonplaceholder.typicode.com/posts/1"
            ]
            
            tabs = []
            for i, site in enumerate(sites):
                print(f"  📑 Creating tab {i+1} for {site}...")
                tab = await browser_session.create_new_tab(site)
                tabs.append(tab)
                await asyncio.sleep(1)
            
            # Switch between tabs and get info
            for i, tab in enumerate(tabs):
                print(f"  🔄 Switching to tab {i+1}...")
                await browser_session.switch_to_tab(i)
                
                current_page = await browser_session.get_current_page()
                title = await current_page.title()
                url = current_page.url
                
                print(f"    ✅ Tab {i+1}: {title}")
                print(f"    🔗 URL: {url}")
                
                # Screenshot each tab
                screenshot_path = Path('demos/screenshots')
                await current_page.screenshot(path=str(screenshot_path / f'07_tab_{i+1}.png'))
            
            print(f"  📊 Total tabs: {len(browser_session.tabs)}")
            return True
            
        except Exception as e:
            print(f"    ❌ Multi-tab demo failed: {e}")
            return False
            
        finally:
            if browser_session:
                await browser_session.close()
    
    async def demo_javascript_execution(self):
        """Demo JavaScript execution"""
        print("⚡ Testing JavaScript execution...")
        
        browser_session = None
        try:
            browser_profile = BrowserProfile(
                headless=False,
                user_data_dir='~/.config/browseruse/profiles/demo_js',
            )
            browser_session = BrowserSession(browser_profile=browser_profile)
            
            page = await browser_session.get_current_page()
            await page.goto("https://example.com")
            await asyncio.sleep(2)
            
            # Execute various JavaScript commands
            js_tests = [
                ("Get page title", "document.title"),
                ("Get URL", "window.location.href"),
                ("Get page height", "document.body.scrollHeight"),
                ("Get viewport width", "window.innerWidth"),
                ("Count links", "document.links.length"),
                ("Get first heading", "document.querySelector('h1')?.textContent || 'No h1 found'")
            ]
            
            for test_name, js_code in js_tests:
                try:
                    result = await browser_session.execute_javascript(js_code)
                    print(f"  ✅ {test_name}: {result}")
                except Exception as e:
                    print(f"  ❌ {test_name}: {e}")
            
            # Take screenshot
            screenshot_path = Path('demos/screenshots')
            await page.screenshot(path=str(screenshot_path / '07_javascript.png'))
            
            return True
            
        except Exception as e:
            print(f"    ❌ JavaScript demo failed: {e}")
            return False
            
        finally:
            if browser_session:
                await browser_session.close()
    
    async def demo_page_interactions(self):
        """Demo page interactions without AI"""
        print("🖱️  Testing page interactions...")
        
        browser_session = None
        try:
            browser_profile = BrowserProfile(
                headless=False,
                user_data_dir='~/.config/browseruse/profiles/demo_interact',
            )
            browser_session = BrowserSession(browser_profile=browser_profile)
            
            page = await browser_session.get_current_page()
            
            # Go to a page with forms
            await page.goto("https://httpbin.org/forms/post")
            await asyncio.sleep(2)
            
            # Fill out form fields using direct Playwright methods
            print("  📝 Filling form fields...")
            
            # Fill text inputs
            await page.fill('input[name="custname"]', 'Test User')
            await page.fill('input[name="custtel"]', '************')
            await page.fill('input[name="custemail"]', '<EMAIL>')
            
            # Select dropdown
            await page.select_option('select[name="size"]', 'medium')
            
            # Check checkbox
            await page.check('input[name="topping"][value="bacon"]')
            
            # Fill textarea
            await page.fill('textarea[name="comments"]', 'This is a test form submission via browser-use!')
            
            print("  ✅ Form filled successfully")
            
            # Take screenshot of filled form
            screenshot_path = Path('demos/screenshots')
            await page.screenshot(path=str(screenshot_path / '07_form_filled.png'))
            
            # Submit form
            print("  📤 Submitting form...")
            await page.click('input[type="submit"]')
            await asyncio.sleep(3)
            
            # Check result
            title = await page.title()
            print(f"  ✅ Form submitted, result page: {title}")
            
            # Screenshot result
            await page.screenshot(path=str(screenshot_path / '07_form_result.png'))
            
            return True
            
        except Exception as e:
            print(f"    ❌ Page interaction demo failed: {e}")
            return False
            
        finally:
            if browser_session:
                await browser_session.close()
    
    async def run_all_demos(self):
        """Run all working feature demos"""
        print("🚀 Starting comprehensive browser automation demo...")
        print("   (No AI agents - just pure browser automation)")
        
        demos = [
            ("Basic Navigation", self.demo_basic_navigation),
            ("Multi-tab Management", self.demo_multi_tab_management),
            ("JavaScript Execution", self.demo_javascript_execution),
            ("Page Interactions", self.demo_page_interactions),
        ]
        
        results = []
        
        for demo_name, demo_func in demos:
            print(f"\n{'='*20} {demo_name} {'='*20}")
            start_time = time.time()
            
            try:
                success = await demo_func()
                duration = time.time() - start_time
                results.append((demo_name, success, duration, None))
                
                if success:
                    print(f"✅ {demo_name} completed in {duration:.1f}s")
                else:
                    print(f"❌ {demo_name} failed after {duration:.1f}s")
                    
            except Exception as e:
                duration = time.time() - start_time
                results.append((demo_name, False, duration, str(e)))
                print(f"💥 {demo_name} crashed after {duration:.1f}s: {e}")
        
        # Print summary
        print(f"\n{'='*60}")
        print("BROWSER AUTOMATION DEMO SUMMARY")
        print(f"{'='*60}")
        
        for demo_name, success, duration, error in results:
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{demo_name:<25} {status:<10} {duration:.1f}s")
            if error:
                print(f"  Error: {error}")
        
        passed = sum(1 for _, success, _, _ in results if success)
        total = len(results)
        
        print(f"\nOverall: {passed}/{total} demos passed")
        print(f"Screenshots saved in: demos/screenshots/")
        
        if passed == total:
            print("🎉 All browser automation features working perfectly!")
        else:
            print("⚠️  Some features need attention")

async def main():
    """Main demo runner"""
    print("=" * 60)
    print("BROWSER-USE DEMO 07: Working Features Demo")
    print("=" * 60)
    
    demo = BrowserDemo()
    await demo.run_all_demos()
    
    print("\nPress Enter to exit...")
    input()

if __name__ == '__main__':
    asyncio.run(main())
