# Browser-Use Demo Test Suite

This directory contains comprehensive tests for the browser-use library, designed to explore and validate all major functionality.

## 🎯 Test Overview

| Test | Description | What It Tests |
|------|-------------|---------------|
| `01_basic_browser_test.py` | Basic browser functionality | Browser launch, navigation, screenshots |
| `02_ai_agent_basic.py` | Simple AI agent tasks | LLM integration, basic web automation |
| `03_chrome_cdp_test.py` | Chrome remote debugging | CDP connection, existing Chrome control |
| `04_multi_model_test.py` | Multiple LLM comparison | OpenAI, Anthropic, Google model performance |
| `05_advanced_features_test.py` | Complex automation | Multi-tab, forms, search, data extraction |
| `06_error_handling_test.py` | Error scenarios | Invalid URLs, timeouts, missing elements |

## 🚀 Quick Start

### Run All Tests
```bash
# Activate virtual environment
source .venv/bin/activate

# Run comprehensive test suite
python demos/run_all_tests.py
```

### Run Individual Tests
```bash
# Basic browser test (no AI required)
python demos/01_basic_browser_test.py

# AI agent test (requires API key)
python demos/02_ai_agent_basic.py

# Chrome CDP test (requires Chrome with --remote-debugging-port=9242)
python demos/03_chrome_cdp_test.py
```

## 📋 Prerequisites

### Required
- Python 3.11+ with browser-use installed
- At least one LLM API key in `.env` file:
  - `OPENAI_API_KEY`
  - `ANTHROPIC_API_KEY` 
  - `GOOGLE_API_KEY`

### Optional
- Chrome running with `--remote-debugging-port=9242` (for CDP tests)

## 📸 Screenshots

All tests save screenshots to `demos/screenshots/` for visual verification of results.

## 🔧 Test Design Philosophy

Each test is designed to be:
- **Standalone**: Can run independently
- **Thorough**: Comprehensive comments explaining what's happening
- **Minimal**: Least code necessary to test functionality
- **Informative**: Clear output about what works vs. what doesn't
- **Visual**: Screenshots for verification

## 📊 Expected Results

### What Should Work
- ✅ Basic browser launch and navigation
- ✅ Simple AI agent tasks with proper API keys
- ✅ Multi-tab handling
- ✅ Form interactions
- ✅ Search and data extraction
- ✅ Error handling for common scenarios

### What Might Fail
- ❌ CDP tests if Chrome isn't running with remote debugging
- ❌ AI tests without proper API keys
- ❌ Network-dependent tests with connectivity issues
- ❌ Complex JavaScript sites (depending on timing)

## 🐛 Troubleshooting

### Common Issues

**"OPENAI_API_KEY not found"**
- Add your API key to `.env` file
- Ensure `.env` is in project root

**"Cannot connect to CDP endpoint"**
- Start Chrome with: `--remote-debugging-port=9242`
- Check if port 9242 is accessible: `curl http://localhost:9242/json/version`

**"Browser launch failed"**
- Check if Playwright browsers are installed: `playwright install`
- Verify virtual environment is activated

**Tests timeout**
- Some tests have 60s timeouts for complex tasks
- Slow networks may cause timeouts
- Consider using faster models (e.g., gpt-4o-mini vs gpt-4o)

## 📈 Performance Notes

- **gpt-4o-mini**: Fastest, cheapest, good for testing
- **claude-3-haiku**: Fast, good balance
- **gpt-4o**: Slower but more capable
- **gemini-1.5-flash**: Good speed/capability balance

## 🎓 Learning from Tests

Each test demonstrates different aspects:
- **Test 01**: Basic Playwright integration
- **Test 02**: LLM + browser automation basics
- **Test 03**: Real browser control via CDP
- **Test 04**: Model comparison and performance
- **Test 05**: Complex multi-step automation
- **Test 06**: Robust error handling

Study the code and output to understand how browser-use works under the hood!
