#!/usr/bin/env python3
"""
Demo 02: AI Agent Basic Test
============================

This script tests basic AI agent functionality:
- Using OpenAI GPT-4 model
- Simple web navigation task
- Agent decision making
- Token usage tracking

Expected: Agent should navigate to a webpage and extract basic information.
"""

import asyncio
import os
from pathlib import Path

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatOpenAI

# import sys
# from loguru import logger as log

# # Configure loguru with color formatting
# log.remove()  # Remove default handler
# log.add(
#     sys.stderr, 
#     format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <cyan>{message}</cyan>",
#     level="INFO"
# )

REGULAR_MODEL = "gpt-4o"
REASONING_MODEL = "o4-mini"

USE_REASONING_MODEL = True
if USE_REASONING_MODEL:
    MODEL = REASONING_MODEL
else:
    MODEL = REGULAR_MODEL



async def test_ai_agent_basic():
    """Test basic AI agent functionality"""
    print("🤖 Starting AI agent basic test...")

    # Verify API key is available
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        return False

    print(f"🔑 Using OpenAI API key: {api_key[:10]}...")

    # Create browser session
    browser_profile = BrowserProfile(
        headless=False,
        user_data_dir="~/.config/browseruse/profiles/test_ai_basic",
    )

    browser_session = None
    try:
        browser_session = BrowserSession(browser_profile=browser_profile)

        # Initialize LLM
        print("🧠 Initializing OpenAI GPT-4...")
        llm = ChatOpenAI(model=MODEL, api_key=api_key)  # Using mini for faster/cheaper testing

        # Create agent with simple task
        task = "Go to example.com and tell me what the main heading says"
        print(f"📋 Task: {task}")

        agent = Agent(
            task=task,
            llm=llm,
            browser_session=browser_session,
        )

        # Run the agent
        print("🚀 Running agent...")
        history = await agent.run()

        # Print results
        print("\n" + "=" * 50)
        print("AGENT RESULTS:")
        print("=" * 50)

        # Print final result
        if history.final_result:
            print(f"✅ Final Result: {history.final_result}")
        else:
            print("❌ No final result returned")

        # Print token usage if available
        if hasattr(history, "usage") and history.usage:
            print(f"💰 Token Usage: {history.usage}")

        # Print number of steps taken
        print(f"👣 Steps taken: {len(history.history)}")

        # Take screenshot of final state
        screenshot_path = Path("demos/screenshots")
        screenshot_path.mkdir(exist_ok=True)

        page = await browser_session.get_current_page()
        await page.screenshot(path=str(screenshot_path / "02_ai_agent_basic.png"))
        print("📸 Screenshot saved")

        print("✅ AI agent basic test completed!")
        return True

    except Exception as e:
        print(f"❌ AI agent basic test failed: {e}")
        import traceback

        traceback.print_exc()
        return False

    finally:
        if browser_session:
            print("🧹 Closing browser session...")
            await browser_session.close()


async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 02: AI Agent Basic Test")
    print("=" * 50)

    success = await test_ai_agent_basic()

    if success:
        print("\n🎉 Test passed!")
    else:
        print("\n💥 Test failed!")

    print("\nPress Enter to exit...")
    input()


if __name__ == "__main__":
    asyncio.run(main())
if __name__ == "__main__":
    asyncio.run(main())
