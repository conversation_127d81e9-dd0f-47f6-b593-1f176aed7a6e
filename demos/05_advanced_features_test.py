#!/usr/bin/env python3
"""
Demo 05: Advanced Features Test
===============================

This script tests advanced browser-use features:
- Multi-tab handling
- Form filling
- File downloads
- Custom actions
- Error handling

Expected: Demonstrates complex browser automation capabilities.
"""

import asyncio
import os
from pathlib import Path

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatOpenAI


async def test_multi_tab_handling():
    """Test opening and managing multiple tabs"""
    print("🗂️  Testing multi-tab handling...")

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY required")
        return False

    browser_session = None
    try:
        browser_profile = BrowserProfile(
            headless=False,
            user_data_dir="~/.config/browseruse/profiles/test_multitab",
        )
        browser_session = BrowserSession(browser_profile=browser_profile)

        llm = ChatOpenAI(model="gpt-4o-mini", api_key=api_key)

        # Task involving multiple tabs
        task = """
        Open 3 different tabs:
        1. Go to example.com in the first tab
        2. Open a new tab and go to httpbin.org
        3. Open another tab and go to jsonplaceholder.typicode.com
        Then tell me the title of each tab
        """

        agent = Agent(task=task, llm=llm, browser_session=browser_session)

        print("🚀 Running multi-tab test...")
        history = await agent.run()

        print(f"✅ Multi-tab test result: {history.final_result}")

        # Take screenshot of final state
        screenshot_path = Path("demos/screenshots")
        screenshot_path.mkdir(exist_ok=True)

        page = await browser_session.get_current_page()
        await page.screenshot(path=str(screenshot_path / "05_multitab_test.png"))

        return True

    except Exception as e:
        print(f"❌ Multi-tab test failed: {e}")
        return False

    finally:
        if browser_session:
            await browser_session.close()


async def test_form_interaction():
    """Test form filling and interaction"""
    print("📝 Testing form interaction...")

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY required")
        return False

    browser_session = None
    try:
        browser_profile = BrowserProfile(
            headless=False,
            user_data_dir="~/.config/browseruse/profiles/test_forms",
        )
        browser_session = BrowserSession(browser_profile=browser_profile)

        llm = ChatOpenAI(model="gpt-4o-mini", api_key=api_key)

        # Task involving form interaction
        task = """
        Go to httpbin.org/forms/post and fill out the form with:
        - Customer name: "Test User"
        - Telephone: "************" 
        - Email: "<EMAIL>"
        - Size: select "Medium"
        - Topping: check "Bacon"
        - Comments: "This is a test form submission"
        Then submit the form and tell me what response you get
        """

        agent = Agent(task=task, llm=llm, browser_session=browser_session)

        print("🚀 Running form interaction test...")
        history = await agent.run()

        print(f"✅ Form test result: {history.final_result}")

        # Take screenshot
        page = await browser_session.get_current_page()
        screenshot_path = Path("demos/screenshots")
        screenshot_path.mkdir(exist_ok=True)
        await page.screenshot(path=str(screenshot_path / "05_form_test.png"))

        return True

    except Exception as e:
        print(f"❌ Form test failed: {e}")
        return False

    finally:
        if browser_session:
            await browser_session.close()


async def test_search_and_extract():
    """Test search functionality and data extraction"""
    print("🔍 Testing search and data extraction...")

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY required")
        return False

    browser_session = None
    try:
        browser_profile = BrowserProfile(
            headless=False,
            user_data_dir="~/.config/browseruse/profiles/test_search",
        )
        browser_session = BrowserSession(browser_profile=browser_profile)

        llm = ChatOpenAI(model="gpt-4o-mini", api_key=api_key)

        # Task involving search and extraction
        task = """
        Go to Wikipedia.org and search for "Python programming language".
        Then extract and tell me:
        1. The first paragraph of the article
        2. When Python was first released
        3. Who created Python
        """

        agent = Agent(task=task, llm=llm, browser_session=browser_session)

        print("🚀 Running search and extract test...")
        history = await agent.run()

        print(f"✅ Search test result: {history.final_result}")

        # Take screenshot
        page = await browser_session.get_current_page()
        screenshot_path = Path("demos/screenshots")
        screenshot_path.mkdir(exist_ok=True)
        await page.screenshot(path=str(screenshot_path / "05_search_test.png"))

        return True

    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return False

    finally:
        if browser_session:
            await browser_session.close()


async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 05: Advanced Features Test")
    print("=" * 50)

    tests = [
        ("Multi-tab handling", test_multi_tab_handling),
        ("Form interaction", test_form_interaction),
        ("Search and extract", test_search_and_extract),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 50)
    print("ADVANCED FEATURES TEST SUMMARY")
    print("=" * 50)

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<25} {status}")

    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")

    print("\nPress Enter to exit...")
    input()


if __name__ == "__main__":
    asyncio.run(main())
if __name__ == "__main__":
    asyncio.run(main())
