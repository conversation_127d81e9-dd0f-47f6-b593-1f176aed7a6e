#!/usr/bin/env python3
"""
Demo 02b: AI Agent with <PERSON> (Alternative)
===========================================

This script tests AI agent functionality using Claude instead of OpenAI
to avoid the reasoning_effort parameter issue.

Expected: Agent should navigate to a webpage and extract basic information.
"""

import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatAnthropic

async def test_claude_agent():
    """Test AI agent with <PERSON>"""
    print("🤖 Starting Claude agent test...")
    
    # Verify API key
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key:
        print("❌ ANTHROPIC_API_KEY not found in environment")
        return False
    
    print(f"🔑 Using Anthropic API key: {api_key[:10]}...")
    
    browser_session = None
    try:
        # Create browser session
        browser_profile = BrowserProfile(
            headless=False,
            user_data_dir='~/.config/browseruse/profiles/test_claude',
        )
        browser_session = BrowserSession(browser_profile=browser_profile)
        
        # Initialize Claude LLM
        print("🧠 Initializing Claude...")
        llm = ChatAnthropic(
            model='claude-3-haiku-20240307',  # Fast, cheap model for testing
            api_key=api_key
        )
        
        # Create agent with simple task
        task = "Go to example.com and tell me what the main heading says"
        print(f"📋 Task: {task}")
        
        agent = Agent(
            task=task,
            llm=llm,
            browser_session=browser_session,
        )
        
        # Run the agent
        print("🚀 Running Claude agent...")
        history = await agent.run()
        
        # Print results
        print("\n" + "="*50)
        print("CLAUDE AGENT RESULTS:")
        print("="*50)
        
        # Print final result
        if history.final_result:
            print(f"✅ Final Result: {history.final_result}")
        else:
            print("❌ No final result returned")
        
        # Print token usage if available
        if hasattr(history, 'usage') and history.usage:
            print(f"💰 Token Usage: {history.usage}")
        
        # Print number of steps taken
        print(f"👣 Steps taken: {len(history.history)}")
        
        # Take screenshot of final state
        screenshot_path = Path('demos/screenshots')
        screenshot_path.mkdir(exist_ok=True)
        
        page = await browser_session.get_current_page()
        await page.screenshot(path=str(screenshot_path / '02b_claude_agent.png'))
        print("📸 Screenshot saved")
        
        print("✅ Claude agent test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Claude agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if browser_session:
            print("🧹 Closing browser session...")
            await browser_session.close()

async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 02b: Claude Agent Test")
    print("=" * 50)
    
    success = await test_claude_agent()
    
    if success:
        print("\n🎉 Test passed!")
    else:
        print("\n💥 Test failed!")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == '__main__':
    asyncio.run(main())
