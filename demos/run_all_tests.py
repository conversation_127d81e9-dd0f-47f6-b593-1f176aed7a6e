#!/usr/bin/env python3
"""
Master Test Runner for Browser-Use Demos
========================================

This script runs all browser-use demo tests in sequence and provides
a comprehensive report of what works and what doesn't.

Usage: python demos/run_all_tests.py
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

class TestRunner:
    """Master test runner for all browser-use demos"""
    
    def __init__(self):
        self.results = []
        self.start_time = time.time()
        
    def check_prerequisites(self):
        """Check if all prerequisites are met"""
        print("🔍 Checking prerequisites...")
        
        issues = []
        
        # Check API keys
        api_keys = {
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
            'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY'),
            'GOOGLE_API_KEY': os.getenv('GOOGLE_API_KEY'),
        }
        
        available_keys = [name for name, key in api_keys.items() if key]
        
        if not available_keys:
            issues.append("❌ No LLM API keys found in .env file")
        else:
            print(f"✅ Found API keys: {', '.join(available_keys)}")
        
        # Check Chrome CDP (optional)
        try:
            import requests
            response = requests.get('http://localhost:9242/json/version', timeout=2)
            if response.status_code == 200:
                print("✅ Chrome CDP available on port 9242")
            else:
                print("⚠️  Chrome CDP not responding (port 9242)")
        except:
            print("⚠️  Chrome CDP not available (optional)")
        
        # Check screenshots directory
        screenshot_dir = Path('demos/screenshots')
        screenshot_dir.mkdir(exist_ok=True)
        print(f"✅ Screenshots directory: {screenshot_dir}")
        
        return len(issues) == 0, issues
    
    async def run_test_module(self, test_name, test_description):
        """Run a single test module"""
        print(f"\n{'='*60}")
        print(f"🧪 RUNNING: {test_name}")
        print(f"📝 {test_description}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            # Import and run the test module
            module_name = f"demos.{test_name}"
            
            # Dynamic import
            if test_name == "01_basic_browser_test":
                from demos import basic_browser_test as test_module
            elif test_name == "02_ai_agent_basic":
                from demos import ai_agent_basic as test_module
            elif test_name == "03_chrome_cdp_test":
                from demos import chrome_cdp_test as test_module
            elif test_name == "04_multi_model_test":
                from demos import multi_model_test as test_module
            elif test_name == "05_advanced_features_test":
                from demos import advanced_features_test as test_module
            elif test_name == "06_error_handling_test":
                from demos import error_handling_test as test_module
            else:
                raise ImportError(f"Unknown test module: {test_name}")
            
            # Run the test's main function
            await test_module.main()
            
            duration = time.time() - start_time
            
            result = {
                'name': test_name,
                'description': test_description,
                'success': True,
                'duration': duration,
                'error': None
            }
            
            print(f"✅ {test_name} completed successfully in {duration:.1f}s")
            
        except Exception as e:
            duration = time.time() - start_time
            
            result = {
                'name': test_name,
                'description': test_description,
                'success': False,
                'duration': duration,
                'error': str(e)
            }
            
            print(f"❌ {test_name} failed after {duration:.1f}s: {e}")
        
        self.results.append(result)
        return result
    
    def print_final_report(self):
        """Print comprehensive final report"""
        total_duration = time.time() - self.start_time
        
        print("\n" + "="*80)
        print("🎯 BROWSER-USE COMPREHENSIVE TEST REPORT")
        print("="*80)
        
        print(f"⏱️  Total test duration: {total_duration:.1f} seconds")
        print(f"📊 Tests run: {len(self.results)}")
        
        successful = [r for r in self.results if r['success']]
        failed = [r for r in self.results if not r['success']]
        
        print(f"✅ Successful: {len(successful)}")
        print(f"❌ Failed: {len(failed)}")
        
        # Detailed results
        print(f"\n{'Test Name':<25} {'Status':<10} {'Duration':<10} {'Description'}")
        print("-" * 80)
        
        for result in self.results:
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            duration = f"{result['duration']:.1f}s"
            description = result['description'][:30] + "..." if len(result['description']) > 30 else result['description']
            
            print(f"{result['name']:<25} {status:<10} {duration:<10} {description}")
        
        # Error details
        if failed:
            print(f"\n❌ FAILED TESTS DETAILS:")
            print("-" * 50)
            for result in failed:
                print(f"• {result['name']}: {result['error']}")
        
        # Success rate
        success_rate = len(successful) / len(self.results) * 100
        print(f"\n🎯 Overall Success Rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("🎉 Perfect! All tests passed!")
        elif success_rate >= 80:
            print("👍 Great! Most functionality is working well.")
        elif success_rate >= 60:
            print("⚠️  Good, but some issues need attention.")
        else:
            print("🔧 Needs work - several core features have issues.")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if not any('01_basic_browser_test' in r['name'] and r['success'] for r in self.results):
            print("• Fix basic browser functionality first")
        if not any('02_ai_agent_basic' in r['name'] and r['success'] for r in self.results):
            print("• Check LLM API keys and basic agent setup")
        if not any('03_chrome_cdp_test' in r['name'] and r['success'] for r in self.results):
            print("• Chrome CDP setup may need attention")
        
        print(f"\n📸 Screenshots saved in: demos/screenshots/")
        print(f"📋 Test logs available above for debugging")

async def main():
    """Main test runner"""
    print("🚀 BROWSER-USE COMPREHENSIVE TEST SUITE")
    print("="*50)
    
    runner = TestRunner()
    
    # Check prerequisites
    prereqs_ok, issues = runner.check_prerequisites()
    
    if not prereqs_ok:
        print("\n❌ Prerequisites not met:")
        for issue in issues:
            print(f"   {issue}")
        print("\nPlease fix these issues before running tests.")
        return
    
    # Define tests to run
    tests = [
        ("01_basic_browser_test", "Basic browser launch and navigation"),
        ("02_ai_agent_basic", "AI agent with simple web task"),
        ("03_chrome_cdp_test", "Chrome remote debugging integration"),
        ("04_multi_model_test", "Multiple LLM model comparison"),
        ("05_advanced_features_test", "Advanced browser automation features"),
        ("06_error_handling_test", "Error handling and edge cases"),
    ]
    
    print(f"\n🎯 Running {len(tests)} test suites...")
    
    # Run each test
    for test_name, test_description in tests:
        try:
            await runner.run_test_module(test_name, test_description)
        except KeyboardInterrupt:
            print(f"\n⏹️  Test suite interrupted by user")
            break
        except Exception as e:
            print(f"\n💥 Unexpected error running {test_name}: {e}")
            continue
    
    # Print final report
    runner.print_final_report()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test suite interrupted. Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        import traceback
        traceback.print_exc()
