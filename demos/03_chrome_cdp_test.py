#!/usr/bin/env python3
"""
Demo 03: Chrome CDP (Remote Debugging) Test
===========================================

This script tests connecting to your existing Chrome browser via CDP:
- Connecting to Chrome on port 9242 (your remote debugging port)
- Using existing Chrome tabs/session
- AI agent working with real Chrome browser

Expected: Should connect to your running Chrome and control it via AI.

Prerequisites: Chrome must be running with --remote-debugging-port=9242
"""

import asyncio
import os
from pathlib import Path

import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from browser_use import Agent, Controller
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatOpenAI


async def test_cdp_connection():
    """Test connection to Chrome via CDP"""
    print("🔗 Testing CDP connection...")

    cdp_url = "http://localhost:9242"

    try:
        # Test if CDP endpoint is accessible
        print(f"🌐 Testing CDP endpoint: {cdp_url}")
        response = requests.get(f"{cdp_url}/json/version", timeout=5)

        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Chrome version: {version_info.get('Browser', 'Unknown')}")
            print(f"✅ WebKit version: {version_info.get('WebKit-Version', 'Unknown')}")
            return True
        else:
            print(f"❌ CDP endpoint returned status: {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to CDP endpoint")
        print("   Make sure Chrome is running with --remote-debugging-port=9242")
        return False
    except Exception as e:
        print(f"❌ CDP connection test failed: {e}")
        return False


async def test_cdp_agent():
    """Test AI agent with CDP connection"""
    print("🤖 Starting CDP agent test...")

    # Verify API key
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY not found")
        return False

    try:
        # Create browser session with CDP
        print("📱 Creating CDP browser session...")
        browser_session = BrowserSession(
            browser_profile=BrowserProfile(headless=False),
            cdp_url="http://localhost:9242",  # Your Chrome's remote debugging port
        )

        # Initialize controller and LLM
        controller = Controller()
        llm = ChatOpenAI(model="gpt-4o-mini", api_key=api_key)

        # Simple task to test CDP functionality
        task = "Open a new tab and go to wikipedia.org, then tell me what you see on the homepage"
        print(f"📋 Task: {task}")

        # Create and run agent
        agent = Agent(
            task=task,
            llm=llm,
            controller=controller,
            browser_session=browser_session,
        )

        print("🚀 Running CDP agent...")
        history = await agent.run()

        # Print results
        print("\n" + "=" * 50)
        print("CDP AGENT RESULTS:")
        print("=" * 50)

        if history.final_result:
            print(f"✅ Final Result: {history.final_result}")
        else:
            print("❌ No final result returned")

        if hasattr(history, "usage") and history.usage:
            print(f"💰 Token Usage: {history.usage}")

        print(f"👣 Steps taken: {len(history.history)}")

        # Take screenshot
        screenshot_path = Path("demos/screenshots")
        screenshot_path.mkdir(exist_ok=True)

        page = await browser_session.get_current_page()
        await page.screenshot(path=str(screenshot_path / "03_cdp_test.png"))
        print("📸 Screenshot saved")

        # Close browser session (but not your Chrome)
        await browser_session.close()

        print("✅ CDP agent test completed!")
        return True

    except Exception as e:
        print(f"❌ CDP agent test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 03: Chrome CDP Test")
    print("=" * 50)

    # First test CDP connection
    cdp_ok = await test_cdp_connection()

    if not cdp_ok:
        print("\n💥 CDP connection failed - skipping agent test")
        print("\nTo fix this:")
        print("1. Make sure Chrome is running")
        print("2. Chrome should be started with: --remote-debugging-port=9242")
        print("3. Try visiting: http://localhost:9242/json/version")
        return

    # If CDP works, test the agent
    agent_ok = await test_cdp_agent()

    if agent_ok:
        print("\n🎉 All CDP tests passed!")
    else:
        print("\n💥 CDP agent test failed!")

    print("\nPress Enter to exit...")
    input()


if __name__ == "__main__":
    asyncio.run(main())
if __name__ == "__main__":
    asyncio.run(main())
