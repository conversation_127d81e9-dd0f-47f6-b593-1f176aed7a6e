#!/usr/bin/env python3
"""
Demo 01: Basic Browser Launch Test
==================================

This script tests the most fundamental browser-use functionality:
- Loading environment variables
- Initializing a browser session
- Opening a simple webpage
- Taking a screenshot
- Closing cleanly

Expected: Should open a browser, navigate to a page, and close without errors.
"""

import asyncio
import os
from pathlib import Path

from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatOpenAI


async def test_basic_browser():
    """Test basic browser functionality without AI agent"""
    print("🚀 Starting basic browser test...")

    # Create browser profile - using default Playwright browser (not your Chrome)
    browser_profile = BrowserProfile(
        headless=False,  # Show browser window for visual confirmation
        user_data_dir="~/.config/browseruse/profiles/test_basic",
    )

    browser_session = None
    try:
        # Initialize browser session
        print("📱 Creating browser session...")
        browser_session = BrowserSession(browser_profile=browser_profile)

        # Get the browser page
        print("🌐 Getting browser page...")
        page = await browser_session.get_current_page()

        # Navigate to a simple webpage
        print("🔗 Navigating to example.com...")
        await page.goto("https://example.com")

        # Wait a moment for page to load
        await asyncio.sleep(2)

        # Take a screenshot to verify it worked
        screenshot_path = Path("demos/screenshots")
        screenshot_path.mkdir(exist_ok=True)

        print("📸 Taking screenshot...")
        await page.screenshot(path=str(screenshot_path / "01_basic_test.png"))

        # Get page title to verify navigation worked
        title = await page.title()
        print(f"✅ Page title: {title}")

        print("✅ Basic browser test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Basic browser test failed: {e}")
        return False

    finally:
        # Always clean up
        if browser_session:
            print("🧹 Closing browser session...")
            await browser_session.close()


async def main():
    """Main test runner"""
    print("=" * 50)
    print("BROWSER-USE DEMO 01: Basic Browser Test")
    print("=" * 50)

    success = await test_basic_browser()

    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")

    print("\nPress Enter to exit...")
    input()


if __name__ == "__main__":
    asyncio.run(main())
if __name__ == "__main__":
    asyncio.run(main())
